import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { userAPI, postsAPI } from "../services/api";
import {
  User,
  Settings,
  MapPin,
  Calendar,
  Heart,
  MessageCircle,
  Share2,
  Edit3,
  Save,
  X,
} from "lucide-react";
import toast from "react-hot-toast";

const Profile = () => {
  const { user, updateUser } = useAuth();
  const [userPosts, setUserPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: "",
    lastName: "",
    bio: "",
    islamicName: "",
    madhab: "",
    location: { city: "", country: "" },
  });
  const [activeTab, setActiveTab] = useState("posts"); // posts, about, settings

  useEffect(() => {
    if (user) {
      setEditForm({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        bio: user.bio || "",
        islamicName: user.islamicName || "",
        madhab: user.madhab || "",
        location: user.location || { city: "", country: "" },
      });
      fetchUserPosts();
    }
  }, [user]);

  const fetchUserPosts = async () => {
    try {
      setIsLoading(true);
      const response = await postsAPI.getPosts(1, "", user._id);
      setUserPosts(response.data.data.posts || []);
    } catch (error) {
      console.error("Failed to fetch user posts:", error);
      toast.error("Failed to load posts");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      const response = await userAPI.updateProfile(editForm);
      updateUser(response.data.data.user);
      setIsEditing(false);
      toast.success("Profile updated successfully!");
    } catch (error) {
      const message =
        error.response?.data?.message || "Failed to update profile";
      toast.error(message);
    }
  };

  const handleLikePost = async (postId) => {
    try {
      await postsAPI.likePost(postId);
      // Refresh posts to update like count
      fetchUserPosts();
    } catch (error) {
      console.error("Failed to like post:", error);
      toast.error("Failed to like post");
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="card p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            {/* Profile Picture */}
            <div className="flex-shrink-0">
              {user.profilePicture ? (
                <img
                  src={user.profilePicture}
                  alt={user.firstName}
                  className="w-24 h-24 rounded-full object-cover"
                />
              ) : (
                <div className="w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-3xl font-bold">
                    {user.firstName?.[0]?.toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) =>
                        setEditForm({ ...editForm, firstName: e.target.value })
                      }
                      className="input-field"
                      placeholder="First Name"
                    />
                    <input
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) =>
                        setEditForm({ ...editForm, lastName: e.target.value })
                      }
                      className="input-field"
                      placeholder="Last Name"
                    />
                  </div>
                  <input
                    type="text"
                    value={editForm.islamicName}
                    onChange={(e) =>
                      setEditForm({ ...editForm, islamicName: e.target.value })
                    }
                    className="input-field"
                    placeholder="Islamic Name (Optional)"
                  />
                  <textarea
                    value={editForm.bio}
                    onChange={(e) =>
                      setEditForm({ ...editForm, bio: e.target.value })
                    }
                    className="input-field"
                    rows={3}
                    placeholder="Tell us about yourself..."
                  />
                  <div className="grid md:grid-cols-2 gap-4">
                    <select
                      value={editForm.madhab}
                      onChange={(e) =>
                        setEditForm({ ...editForm, madhab: e.target.value })
                      }
                      className="input-field"
                    >
                      <option value="">Select Madhab</option>
                      <option value="Hanafi">Hanafi</option>
                      <option value="Maliki">Maliki</option>
                      <option value="Shafi">Shafi</option>
                      <option value="Hanbali">Hanbali</option>
                      <option value="Other">Other</option>
                    </select>
                    <input
                      type="text"
                      value={editForm.location.city}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          location: {
                            ...editForm.location,
                            city: e.target.value,
                          },
                        })
                      }
                      className="input-field"
                      placeholder="City"
                    />
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={handleSaveProfile}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <Save size={16} />
                      <span>Save Changes</span>
                    </button>
                    <button
                      onClick={() => setIsEditing(false)}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <X size={16} />
                      <span>Cancel</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                        {user.firstName} {user.lastName}
                      </h1>
                      {user.islamicName && (
                        <p className="text-xl text-primary-600 arabic-text">
                          {user.islamicName}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <Edit3 size={16} />
                      <span>Edit Profile</span>
                    </button>
                  </div>

                  {user.bio && (
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {user.bio}
                    </p>
                  )}

                  <div className="flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400">
                    {user.madhab && (
                      <span className="flex items-center">
                        <User size={16} className="mr-1" />
                        {user.madhab} Madhab
                      </span>
                    )}
                    {user.location?.city && (
                      <span className="flex items-center">
                        <MapPin size={16} className="mr-1" />
                        {user.location.city}
                        {user.location.country && `, ${user.location.country}`}
                      </span>
                    )}
                    <span className="flex items-center">
                      <Calendar size={16} className="mr-1" />
                      Joined {formatDate(user.createdAt)}
                    </span>
                  </div>

                  {/* Stats */}
                  <div className="flex space-x-6 mt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600">
                        {userPosts.length}
                      </div>
                      <div className="text-sm text-gray-500">Posts</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600">
                        {user.followerCount || 0}
                      </div>
                      <div className="text-sm text-gray-500">Followers</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600">
                        {user.followingCount || 0}
                      </div>
                      <div className="text-sm text-gray-500">Following</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: "posts", label: "Posts", icon: MessageCircle },
                { id: "about", label: "About", icon: User },
                { id: "settings", label: "Settings", icon: Settings },
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  <Icon size={18} />
                  <span>{label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "posts" && (
          <div className="space-y-6">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              </div>
            ) : userPosts.length > 0 ? (
              userPosts.map((post) => (
                <div key={post._id} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user.firstName?.[0]?.toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">
                          {user.firstName} {user.lastName}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(post.createdAt)}
                        </p>
                      </div>
                    </div>
                    <span className="text-xs px-2 py-1 bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 rounded-full">
                      {post.content.type}
                    </span>
                  </div>

                  {/* Post Content */}
                  {post.content.text && (
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      {post.content.text}
                    </p>
                  )}

                  {/* Islamic Content */}
                  {post.islamicContent?.quranVerse && (
                    <div className="bg-primary-50 dark:bg-primary-900/20 p-4 rounded-lg mb-4">
                      <div className="arabic-text text-xl mb-2">
                        {post.islamicContent.quranVerse.text}
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-2">
                        {post.islamicContent.quranVerse.translation}
                      </p>
                      <p className="text-sm text-primary-600">
                        Quran {post.islamicContent.quranVerse.surah}:
                        {post.islamicContent.quranVerse.ayah}
                      </p>
                    </div>
                  )}

                  {/* Post Images */}
                  {post.content.images && post.content.images.length > 0 && (
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      {post.content.images.slice(0, 4).map((image, index) => (
                        <img
                          key={index}
                          src={image.url}
                          alt={image.caption || "Post image"}
                          className="rounded-lg object-cover h-48 w-full"
                        />
                      ))}
                    </div>
                  )}

                  {/* Post Actions */}
                  <div className="flex items-center space-x-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={() => handleLikePost(post._id)}
                      className={`flex items-center space-x-2 text-sm ${
                        post.isLikedBy?.(user._id)
                          ? "text-red-600"
                          : "text-gray-500 hover:text-red-600"
                      }`}
                    >
                      <Heart
                        size={16}
                        className={
                          post.isLikedBy?.(user._id) ? "fill-current" : ""
                        }
                      />
                      <span>{post.likeCount || 0}</span>
                    </button>

                    <button className="flex items-center space-x-2 text-sm text-gray-500 hover:text-primary-600">
                      <MessageCircle size={16} />
                      <span>{post.commentCount || 0}</span>
                    </button>

                    <button className="flex items-center space-x-2 text-sm text-gray-500 hover:text-primary-600">
                      <Share2 size={16} />
                      <span>Share</span>
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No posts yet
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Start sharing your thoughts and reflections with the
                  community!
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === "about" && (
          <div className="card p-8">
            <h2 className="text-2xl font-bold mb-6">About</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Personal Information
                </h3>
                <div className="grid md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {user.firstName}{" "}
                    {user.lastName}
                  </div>
                  {user.islamicName && (
                    <div>
                      <span className="font-medium">Islamic Name:</span>{" "}
                      {user.islamicName}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Email:</span> {user.email}
                  </div>
                  {user.madhab && (
                    <div>
                      <span className="font-medium">Madhab:</span> {user.madhab}
                    </div>
                  )}
                </div>
              </div>

              {user.bio && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Bio</h3>
                  <p className="text-gray-600 dark:text-gray-300">{user.bio}</p>
                </div>
              )}

              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Account Information
                </h3>
                <div className="grid md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Joined:</span>{" "}
                    {formatDate(user.createdAt)}
                  </div>
                  <div>
                    <span className="font-medium">Last Active:</span>{" "}
                    {formatDate(user.lastActive)}
                  </div>
                  <div>
                    <span className="font-medium">Auth Provider:</span>{" "}
                    {user.authProvider}
                  </div>
                  <div>
                    <span className="font-medium">Email Verified:</span>{" "}
                    {user.isEmailVerified ? "Yes" : "No"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "settings" && (
          <div className="space-y-6">
            <div className="card p-8">
              <h2 className="text-2xl font-bold mb-6">Privacy Settings</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Profile Visibility</h3>
                    <p className="text-sm text-gray-500">
                      Who can see your profile
                    </p>
                  </div>
                  <select className="input-field max-w-xs">
                    <option value="public">Public</option>
                    <option value="followers">Followers Only</option>
                    <option value="private">Private</option>
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Show Location</h3>
                    <p className="text-sm text-gray-500">
                      Display your city on profile
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Allow Messages</h3>
                    <p className="text-sm text-gray-500">
                      Let others send you messages
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" defaultChecked />
                </div>
              </div>
            </div>

            <div className="card p-8">
              <h2 className="text-2xl font-bold mb-6">Notification Settings</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Email Notifications</h3>
                    <p className="text-sm text-gray-500">
                      Receive notifications via email
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Post Notifications</h3>
                    <p className="text-sm text-gray-500">
                      Get notified about likes and comments
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Event Reminders</h3>
                    <p className="text-sm text-gray-500">
                      Reminders for upcoming events
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" defaultChecked />
                </div>
              </div>
            </div>

            <div className="card p-8">
              <h2 className="text-2xl font-bold mb-6">
                Prayer Time Preferences
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Calculation Method
                  </label>
                  <select className="input-field">
                    <option value="2">
                      Islamic Society of North America (ISNA)
                    </option>
                    <option value="1">
                      University of Islamic Sciences, Karachi
                    </option>
                    <option value="3">Muslim World League</option>
                    <option value="4">Umm Al-Qura University, Makkah</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Madhab for Asr
                  </label>
                  <select className="input-field">
                    <option value="0">Shafi, Maliki, Hanbali</option>
                    <option value="1">Hanafi</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
