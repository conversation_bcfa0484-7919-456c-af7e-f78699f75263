!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("react-hook-form"),require("zod/v4/core")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form","zod/v4/core"],r):r((e||self).hookformResolversZod={},e.hookformResolvers,e.<PERSON>actHookForm,e.z4)}(this,function(e,r,o,n){function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach(function(o){if("default"!==o){var n=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(r,o,n.get?n:{enumerable:!0,get:function(){return e[o]}})}}),r.default=e,r}var s=/*#__PURE__*/t(n);function i(e,r){try{var o=e()}catch(e){return r(e)}return o&&o.then?o.then(void 0,r):o}function a(e,r){for(var n={};e.length;){var t=e[0],s=t.code,i=t.message,a=t.path.join(".");if(!n[a])if("unionErrors"in t){var u=t.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:s};if("unionErrors"in t&&t.unionErrors.forEach(function(r){return r.errors.forEach(function(r){return e.push(r)})}),r){var c=n[a].types,f=c&&c[t.code];n[a]=o.appendErrors(a,r,n,s,f?[].concat(f,t.message):t.message)}e.shift()}return n}function u(e,r){for(var n={};e.length;){var t=e[0],s=t.code,i=t.message,a=t.path.join(".");if(!n[a])if("invalid_union"===t.code){var u=t.errors[0][0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:s};if("invalid_union"===t.code&&t.errors.forEach(function(r){return r.forEach(function(r){return e.push(r)})}),r){var c=n[a].types,f=c&&c[t.code];n[a]=o.appendErrors(a,r,n,s,f?[].concat(f,t.message):t.message)}e.shift()}return n}e.zodResolver=function(e,o,n){if(void 0===n&&(n={}),function(e){return"_def"in e&&"object"==typeof e._def&&"typeName"in e._def}(e))return function(t,s,u){try{return Promise.resolve(i(function(){return Promise.resolve(e["sync"===n.mode?"parse":"parseAsync"](t,o)).then(function(e){return u.shouldUseNativeValidation&&r.validateFieldsNatively({},u),{errors:{},values:n.raw?Object.assign({},t):e}})},function(e){if(function(e){return Array.isArray(null==e?void 0:e.issues)}(e))return{values:{},errors:r.toNestErrors(a(e.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw e}))}catch(e){return Promise.reject(e)}};if(function(e){return"_zod"in e&&"object"==typeof e._zod}(e))return function(t,a,c){try{return Promise.resolve(i(function(){return Promise.resolve(("sync"===n.mode?s.parse:s.parseAsync)(e,t,o)).then(function(e){return c.shouldUseNativeValidation&&r.validateFieldsNatively({},c),{errors:{},values:n.raw?Object.assign({},t):e}})},function(e){if(function(e){return e instanceof s.$ZodError}(e))return{values:{},errors:r.toNestErrors(u(e.issues,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw e}))}catch(e){return Promise.reject(e)}};throw new Error("Invalid input: not a Zod schema")}});
//# sourceMappingURL=zod.umd.js.map
