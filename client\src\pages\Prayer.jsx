import { useState, useEffect } from "react";
import { prayerAPI } from "../services/api";
import {
  Clock,
  MapPin,
  Compass,
  Calendar,
  Settings,
  RefreshCw,
} from "lucide-react";
import toast from "react-hot-toast";

const Prayer = () => {
  const [prayerTimes, setPrayerTimes] = useState(null);
  const [nextPrayer, setNextPrayer] = useState(null);
  const [hijriDate, setHijriDate] = useState(null);
  const [qiblaDirection, setQiblaDirection] = useState(null);
  const [location, setLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [calculationMethod, setCalculationMethod] = useState(2); // ISNA
  const [madhab, setMadhab] = useState(0); // Shafi
  const [methods, setMethods] = useState([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    fetchCalculationMethods();
    getUserLocation();
  }, []);

  useEffect(() => {
    if (location) {
      fetchPrayerData();
    }
  }, [location, calculationMethod, madhab]);

  const fetchCalculationMethods = async () => {
    try {
      const response = await prayerAPI.getCalculationMethods();
      setMethods(
        Object.entries(response.data.data).map(([key, value]) => ({
          id: key,
          name: value.name,
        }))
      );
    } catch (error) {
      console.error("Failed to fetch calculation methods:", error);
    }
  };

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => {
          console.error("Geolocation error:", error);
          toast.error(
            "Please enable location access for accurate prayer times"
          );
          setIsLoading(false);
        }
      );
    } else {
      toast.error("Geolocation is not supported by this browser");
      setIsLoading(false);
    }
  };

  const fetchPrayerData = async () => {
    try {
      setIsLoading(true);
      const [prayerRes, nextPrayerRes, hijriRes, qiblaRes] = await Promise.all([
        prayerAPI.getPrayerTimes(
          location.latitude,
          location.longitude,
          calculationMethod,
          madhab
        ),
        prayerAPI.getNextPrayer(
          location.latitude,
          location.longitude,
          calculationMethod,
          madhab
        ),
        prayerAPI.getHijriCalendar(),
        prayerAPI.getQiblaDirection(location.latitude, location.longitude),
      ]);

      setPrayerTimes(prayerRes.data.data);
      setNextPrayer(nextPrayerRes.data.data);
      setHijriDate(hijriRes.data.data);
      setQiblaDirection(qiblaRes.data.data);
    } catch (error) {
      console.error("Failed to fetch prayer data:", error);
      toast.error("Failed to load prayer times");
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(":");
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const getTimeUntilNext = (nextPrayerTime) => {
    if (!nextPrayerTime) return "";

    const now = new Date();
    const [hours, minutes] = nextPrayerTime.split(":");
    const prayerDate = new Date();
    prayerDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    if (prayerDate < now) {
      prayerDate.setDate(prayerDate.getDate() + 1);
    }

    const diff = prayerDate - now;
    const hoursLeft = Math.floor(diff / (1000 * 60 * 60));
    const minutesLeft = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return `${hoursLeft}h ${minutesLeft}m`;
  };

  const prayerNames = ["Fajr", "Dhuhr", "Asr", "Maghrib", "Isha"];

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Prayer Times
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Stay connected with your daily prayers
          </p>
        </div>

        {/* Current Time & Date */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="card p-6 text-center">
            <Clock className="mx-auto h-8 w-8 text-primary-600 mb-4" />
            <h2 className="text-2xl font-bold mb-2">Current Time</h2>
            <p className="text-3xl font-mono text-primary-600">
              {currentTime.toLocaleTimeString()}
            </p>
            <p className="text-gray-500 mt-2">
              {currentTime.toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>

          {hijriDate && (
            <div className="card p-6 text-center">
              <Calendar className="mx-auto h-8 w-8 text-islamic-green mb-4" />
              <h2 className="text-2xl font-bold mb-2">Hijri Date</h2>
              <p className="text-2xl text-islamic-green">
                {hijriDate.hijri.day} {hijriDate.hijri.month.en}{" "}
                {hijriDate.hijri.year}
              </p>
              <p className="text-gray-500 mt-2">{hijriDate.hijri.weekday.en}</p>
            </div>
          )}
        </div>

        {/* Next Prayer */}
        {nextPrayer?.nextPrayer && (
          <div className="card p-8 mb-8 text-center bg-gradient-to-r from-primary-50 to-islamic-green/10 dark:from-primary-900/20 dark:to-islamic-green/10">
            <h2 className="text-2xl font-bold mb-4">Next Prayer</h2>
            <div className="text-4xl font-bold text-primary-600 mb-2">
              {nextPrayer.nextPrayer.name}
            </div>
            <div className="text-2xl mb-2">
              {formatTime(nextPrayer.nextPrayer.time)}
            </div>
            <div className="text-lg text-gray-600 dark:text-gray-300">
              in {getTimeUntilNext(nextPrayer.nextPrayer.time)}
            </div>
          </div>
        )}

        {/* Prayer Times Grid */}
        {prayerTimes && (
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
            {prayerNames.map((prayer) => {
              const time = prayerTimes.timings[prayer];
              const isNext = nextPrayer?.nextPrayer?.name === prayer;

              return (
                <div
                  key={prayer}
                  className={`card p-6 text-center ${
                    isNext
                      ? "ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-900/20"
                      : ""
                  }`}
                >
                  <h3 className="text-lg font-semibold mb-2">{prayer}</h3>
                  <p className="text-2xl font-mono text-primary-600">
                    {formatTime(time)}
                  </p>
                  {isNext && (
                    <p className="text-sm text-primary-600 mt-2 font-medium">
                      Next Prayer
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Additional Info Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Qibla Direction */}
          {qiblaDirection && (
            <div className="card p-6 text-center">
              <Compass className="mx-auto h-8 w-8 text-islamic-green mb-4" />
              <h3 className="text-xl font-bold mb-2">Qibla Direction</h3>
              <p className="text-3xl font-bold text-islamic-green mb-2">
                {Math.round(qiblaDirection.direction)}°
              </p>
              <p className="text-gray-500">from North</p>
            </div>
          )}

          {/* Sunrise */}
          {prayerTimes && (
            <div className="card p-6 text-center">
              <div className="mx-auto h-8 w-8 bg-yellow-500 rounded-full mb-4"></div>
              <h3 className="text-xl font-bold mb-2">Sunrise</h3>
              <p className="text-2xl font-mono text-yellow-600">
                {formatTime(prayerTimes.timings.Sunrise)}
              </p>
            </div>
          )}

          {/* Sunset */}
          {prayerTimes && (
            <div className="card p-6 text-center">
              <div className="mx-auto h-8 w-8 bg-orange-500 rounded-full mb-4"></div>
              <h3 className="text-xl font-bold mb-2">Sunset</h3>
              <p className="text-2xl font-mono text-orange-600">
                {formatTime(prayerTimes.timings.Sunset)}
              </p>
            </div>
          )}
        </div>

        {/* Settings */}
        <div className="card p-6 mb-8">
          <div className="flex items-center mb-6">
            <Settings className="h-6 w-6 text-gray-600 mr-2" />
            <h3 className="text-xl font-bold">Prayer Time Settings</h3>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Calculation Method
              </label>
              <select
                value={calculationMethod}
                onChange={(e) => setCalculationMethod(Number(e.target.value))}
                className="input-field"
              >
                {methods.map((method) => (
                  <option key={method.id} value={method.id}>
                    {method.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Madhab (Asr Calculation)
              </label>
              <select
                value={madhab}
                onChange={(e) => setMadhab(Number(e.target.value))}
                className="input-field"
              >
                <option value={0}>Shafi, Maliki, Hanbali</option>
                <option value={1}>Hanafi</option>
              </select>
            </div>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={fetchPrayerData}
              className="btn-primary flex items-center space-x-2"
            >
              <RefreshCw size={16} />
              <span>Refresh Prayer Times</span>
            </button>
          </div>
        </div>

        {/* Location Info */}
        {location && (
          <div className="card p-6 text-center">
            <MapPin className="mx-auto h-6 w-6 text-gray-600 mb-2" />
            <p className="text-sm text-gray-500">
              Prayer times calculated for your location
            </p>
            <p className="text-xs text-gray-400 mt-1">
              {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
            </p>
          </div>
        )}

        {/* Islamic Calendar Events */}
        {hijriDate?.hijri?.holidays && hijriDate.hijri.holidays.length > 0 && (
          <div className="card p-6 mt-8">
            <h3 className="text-xl font-bold mb-4">Islamic Events Today</h3>
            <div className="space-y-2">
              {hijriDate.hijri.holidays.map((holiday, index) => (
                <div key={index} className="p-3 bg-islamic-green/10 rounded-lg">
                  <p className="font-medium text-islamic-green">{holiday}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Prayer;
