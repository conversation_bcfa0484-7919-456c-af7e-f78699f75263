# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/ummahconnect
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/ummahconnect

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Session Secret
SESSION_SECRET=your_session_secret_key_here

# Email Configuration (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=zlzp giun hqxn uvwo
ADMIN_EMAIL=<EMAIL>

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=dtbfp3ed1
CLOUDINARY_API_KEY=683492736154629
CLOUDINARY_API_SECRET=huHwZ5gCOrNtCe3i1GdK3Llb6PA

# Frontend URL
CLIENT_URL=http://localhost:5173

# API Keys
ALADHAN_API_URL=https://api.aladhan.com/v1
QURAN_API_URL=https://api.quran.com/api/v4
