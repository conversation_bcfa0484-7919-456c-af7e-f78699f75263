import{disposables as u}from'../../utils/disposables.js';import*as o from'../../utils/dom.js';import{isIOS as p}from'../../utils/platform.js';function w(){return p()?{before({doc:n,d:l,meta:f}){function i(a){return f.containers.flatMap(r=>r()).some(r=>r.contains(a))}l.microTask(()=>{var c;if(window.getComputedStyle(n.documentElement).scrollBehavior!=="auto"){let t=u();t.style(n.documentElement,"scrollBehavior","auto"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(c=window.scrollY)!=null?c:window.pageYOffset,r=null;l.addEventListener(n,"click",t=>{if(o.isHTMLorSVGElement(t.target))try{let e=t.target.closest("a");if(!e)return;let{hash:m}=new URL(e.href),s=n.querySelector(m);o.isHTMLorSVGElement(s)&&!i(s)&&(r=s)}catch{}},!0),l.addEventListener(n,"touchstart",t=>{if(o.isHTMLorSVGElement(t.target)&&o.hasInlineStyle(t.target))if(i(t.target)){let e=t.target;for(;e.parentElement&&i(e.parentElement);)e=e.parentElement;l.style(e,"overscrollBehavior","contain")}else l.style(t.target,"touchAction","none")}),l.addEventListener(n,"touchmove",t=>{if(o.isHTMLorSVGElement(t.target)){if(o.isHTMLInputElement(t.target))return;if(i(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==""&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===""&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),r&&r.isConnected&&(r.scrollIntoView({block:"nearest"}),r=null)})})}}:{}}export{w as handleIOSLocking};
