{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAkBM,SAAS,0CAAY,KAAoB;IAC9C,OAAO;QACL,eAAe,MAAM,UAAU,GAAG,CAAC,IAAI;YACrC,WAAW,CAAA,GAAA,4CAAiB,EAAE,MAAM,SAAS;YAC7C,SAAS,CAAA,GAAA,4CAAiB,EAAE,MAAM,OAAO;QAC3C;IACF;AACF", "sources": ["packages/@react-aria/interactions/src/useKeyboard.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {createEventHandler} from './createEventHandler';\nimport {DOMAttributes, KeyboardEvents} from '@react-types/shared';\n\nexport interface KeyboardProps extends KeyboardEvents {\n  /** Whether the keyboard events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface KeyboardResult {\n  /** Props to spread onto the target element. */\n  keyboardProps: DOMAttributes\n}\n\n/**\n * Handles keyboard interactions for a focusable element.\n */\nexport function useKeyboard(props: KeyboardProps): KeyboardResult {\n  return {\n    keyboardProps: props.isDisabled ? {} : {\n      onKeyDown: createEventHand<PERSON>(props.onKeyDown),\n      onKeyUp: createEvent<PERSON>and<PERSON>(props.onKeyUp)\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useKeyboard.main.js.map"}