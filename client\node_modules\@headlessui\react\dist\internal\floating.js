import{autoUpdate as Z,flip as ee,inner as te,offset as ne,shift as re,size as le,useFloating as oe,useInnerOffset as ie,useInteractions as se}from"@floating-ui/react";import*as j from"react";import{createContext as _,use<PERSON><PERSON>back as ae,useContext as T,useMemo as R,useRef as ue,useState as v}from"react";import{useDisposables as fe}from'../hooks/use-disposables.js';import{useEvent as z}from'../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../hooks/use-iso-morphic-effect.js';import*as pe from'../utils/dom.js';let y=_({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});y.displayName="FloatingContext";let $=_(null);$.displayName="PlacementContext";function ye(e){return R(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function Fe(){return T(y).setReference}function be(){return T(y).getReferenceProps}function Te(){let{getFloatingProps:e,slot:t}=T(y);return ae((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor}),[e,t])}function Re(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let t=T($),n=R(()=>e,[JSON.stringify(e,(l,o)=>{var u;return(u=o==null?void 0:o.outerHTML)!=null?u:o})]);C(()=>{t==null||t(n!=null?n:null)},[t,n]);let r=T(y);return R(()=>[r.setFloating,e?r.styles:{}],[r.setFloating,e,r.styles])}let D=4;function Ae({children:e,enabled:t=!0}){let[n,r]=v(null),[l,o]=v(0),u=ue(null),[f,s]=v(null);ce(f);let i=t&&n!==null&&f!==null,{to:F="bottom",gap:E=0,offset:A=0,padding:c=0,inner:h}=ge(n,f),[a,p="center"]=F.split(" ");C(()=>{i&&o(0)},[i]);let{refs:b,floatingStyles:S,context:g}=oe({open:i,placement:a==="selection"?p==="center"?"bottom":`bottom-${p}`:p==="center"?`${a}`:`${a}-${p}`,strategy:"absolute",transform:!1,middleware:[ne({mainAxis:a==="selection"?0:E,crossAxis:A}),re({padding:c}),a!=="selection"&&ee({padding:c}),a==="selection"&&h?te({...h,padding:c,overflowRef:u,offset:l,minItemsVisible:D,referenceOverflowThreshold:c,onFallbackChange(P){var L,N;if(!P)return;let d=g.elements.floating;if(!d)return;let M=parseFloat(getComputedStyle(d).scrollPaddingBottom)||0,I=Math.min(D,d.childElementCount),W=0,B=0;for(let m of(N=(L=g.elements.floating)==null?void 0:L.childNodes)!=null?N:[])if(pe.isHTMLElement(m)){let x=m.offsetTop,k=x+m.clientHeight+M,H=d.scrollTop,U=H+d.clientHeight;if(x>=H&&k<=U)I--;else{B=Math.max(0,Math.min(k,U)-Math.max(x,H)),W=m.clientHeight;break}}I>=1&&o(m=>{let x=W*I-B+M;return m>=x?m:x})}}):null,le({padding:c,apply({availableWidth:P,availableHeight:d,elements:M}){Object.assign(M.floating.style,{overflow:"auto",maxWidth:`${P}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${d}px)`})}})].filter(Boolean),whileElementsMounted:Z}),[w=a,V=p]=g.placement.split("-");a==="selection"&&(w="selection");let G=R(()=>({anchor:[w,V].filter(Boolean).join(" ")}),[w,V]),K=ie(g,{overflowRef:u,onChange:o}),{getReferenceProps:Q,getFloatingProps:X}=se([K]),Y=z(P=>{s(P),b.setFloating(P)});return j.createElement($.Provider,{value:r},j.createElement(y.Provider,{value:{setFloating:Y,setReference:b.setReference,styles:S,getReferenceProps:Q,getFloatingProps:X,slot:G}},e))}function ce(e){C(()=>{if(!e)return;let t=new MutationObserver(()=>{let n=window.getComputedStyle(e).maxHeight,r=parseFloat(n);if(isNaN(r))return;let l=parseInt(n);isNaN(l)||r!==l&&(e.style.maxHeight=`${Math.ceil(r)}px`)});return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}},[e])}function ge(e,t){var o,u,f;let n=O((o=e==null?void 0:e.gap)!=null?o:"var(--anchor-gap, 0)",t),r=O((u=e==null?void 0:e.offset)!=null?u:"var(--anchor-offset, 0)",t),l=O((f=e==null?void 0:e.padding)!=null?f:"var(--anchor-padding, 0)",t);return{...e,gap:n,offset:r,padding:l}}function O(e,t,n=void 0){let r=fe(),l=z((s,i)=>{if(s==null)return[n,null];if(typeof s=="number")return[s,null];if(typeof s=="string"){if(!i)return[n,null];let F=J(s,i);return[F,E=>{let A=q(s);{let c=A.map(h=>window.getComputedStyle(i).getPropertyValue(h));r.requestAnimationFrame(function h(){r.nextFrame(h);let a=!1;for(let[b,S]of A.entries()){let g=window.getComputedStyle(i).getPropertyValue(S);if(c[b]!==g){c[b]=g,a=!0;break}}if(!a)return;let p=J(s,i);F!==p&&(E(p),F=p)})}return r.dispose}]}return[n,null]}),o=R(()=>l(e,t)[0],[e,t]),[u=o,f]=v();return C(()=>{let[s,i]=l(e,t);if(f(s),!!i)return i(f)},[e,t]),u}function q(e){let t=/var\((.*)\)/.exec(e);if(t){let n=t[1].indexOf(",");if(n===-1)return[t[1]];let r=t[1].slice(0,n).trim(),l=t[1].slice(n+1).trim();return l?[r,...q(l)]:[r]}return[]}function J(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}export{Ae as FloatingProvider,Re as useFloatingPanel,Te as useFloatingPanelProps,Fe as useFloatingReference,be as useFloatingReferenceProps,ye as useResolvedAnchor};
