import { useState, useEffect } from "react";
import { quranAPI } from "../services/api";
import {
  Search,
  BookOpen,
  Play,
  Pause,
  Volume2,
  Heart,
  Share2,
  Copy,
} from "lucide-react";
import toast from "react-hot-toast";

const Quran = () => {
  const [chapters, setChapters] = useState([]);
  const [selectedChapter, setSelectedChapter] = useState(null);
  const [verses, setVerses] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [translations, setTranslations] = useState([]);
  const [selectedTranslation, setSelectedTranslation] = useState(131); // Sahih International
  const [dailyVerse, setDailyVerse] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState("chapters"); // chapters, search, daily

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      const [chaptersRes, translationsRes, dailyVerseRes] = await Promise.all([
        quranAPI.getChapters(),
        quranAPI.getTranslations(),
        quranAPI.getDailyVerse(selectedTranslation),
      ]);

      setChapters(chaptersRes.data.data.chapters || []);
      setTranslations(translationsRes.data.data.translations || []);
      setDailyVerse(dailyVerseRes.data.data);
    } catch (error) {
      console.error("Failed to fetch Quran data:", error);
      toast.error("Failed to load Quran data");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchVerses = async (chapterId, page = 1) => {
    try {
      setIsLoading(true);
      const response = await quranAPI.getVerses(
        chapterId,
        page,
        selectedTranslation
      );
      setVerses(response.data.data.verses || []);
      setSelectedChapter(chapters.find((ch) => ch.id === chapterId));
      setCurrentPage(page);
    } catch (error) {
      console.error("Failed to fetch verses:", error);
      toast.error("Failed to load verses");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setIsSearching(true);
      const response = await quranAPI.searchVerses(
        searchQuery,
        1,
        selectedTranslation
      );
      setSearchResults(response.data.data.search.results || []);
      setActiveTab("search");
    } catch (error) {
      console.error("Search failed:", error);
      toast.error("Search failed");
    } finally {
      setIsSearching(false);
    }
  };

  const copyVerse = (verse) => {
    const text = `${verse.text_uthmani}\n\n${verse.translations[0]?.text}\n\nQuran ${verse.verse_key}`;
    navigator.clipboard.writeText(text);
    toast.success("Verse copied to clipboard");
  };

  const shareVerse = (verse) => {
    if (navigator.share) {
      navigator.share({
        title: `Quran ${verse.verse_key}`,
        text: `${verse.translations[0]?.text}\n\nQuran ${verse.verse_key}`,
      });
    } else {
      copyVerse(verse);
    }
  };

  if (isLoading && chapters.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            القرآن الكريم
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            The Noble Quran - Read, Search, and Reflect
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                placeholder="Search in the Quran..."
                className="input-field pl-10 pr-20"
              />
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <button
                onClick={handleSearch}
                disabled={isSearching || !searchQuery.trim()}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary px-4 py-1 text-sm"
              >
                {isSearching ? "Searching..." : "Search"}
              </button>
            </div>
          </div>
        </div>

        {/* Translation Selector */}
        <div className="mb-8 text-center">
          <select
            value={selectedTranslation}
            onChange={(e) => setSelectedTranslation(Number(e.target.value))}
            className="input-field max-w-xs"
          >
            {translations.slice(0, 10).map((translation) => (
              <option key={translation.id} value={translation.id}>
                {translation.name} - {translation.author_name}
              </option>
            ))}
          </select>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: "chapters", label: "Chapters (Surahs)", icon: BookOpen },
                { id: "search", label: "Search Results", icon: Search },
                { id: "daily", label: "Daily Verse", icon: Heart },
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  <Icon size={18} />
                  <span>{label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        {activeTab === "chapters" && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {chapters.map((chapter) => (
              <div
                key={chapter.id}
                onClick={() => fetchVerses(chapter.id)}
                className="card p-6 cursor-pointer hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="text-2xl font-bold text-primary-600">
                    {chapter.id}
                  </div>
                  <div className="text-sm text-gray-500">
                    {chapter.revelation_place} • {chapter.verses_count} verses
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-2 arabic-text">
                  {chapter.name_arabic}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {chapter.name_simple} ({chapter.translated_name?.name})
                </p>
              </div>
            ))}
          </div>
        )}

        {activeTab === "search" && (
          <div className="space-y-6">
            {searchResults.length > 0 ? (
              searchResults.map((result) => (
                <div key={result.verse_key} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium text-primary-600">
                      Surah {result.verse_key}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => copyVerse(result)}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Copy size={16} />
                      </button>
                      <button
                        onClick={() => shareVerse(result)}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Share2 size={16} />
                      </button>
                    </div>
                  </div>
                  <div className="arabic-text text-2xl mb-4 leading-relaxed">
                    {result.text}
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    {result.translations?.[0]?.text}
                  </p>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery
                    ? "No results found"
                    : "Enter a search term to find verses"}
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === "daily" && dailyVerse && (
          <div className="max-w-4xl mx-auto">
            <div className="card p-8 text-center">
              <h2 className="text-2xl font-bold mb-6 text-primary-600">
                Daily Verse - {new Date().toLocaleDateString()}
              </h2>
              <div className="arabic-text text-3xl mb-6 leading-relaxed">
                {dailyVerse.verse?.text_uthmani}
              </div>
              <p className="text-xl text-gray-700 dark:text-gray-300 mb-4">
                {dailyVerse.verse?.translations?.[0]?.text}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Surah {dailyVerse.verse?.chapter_id}, Verse{" "}
                {dailyVerse.verse?.verse_number}
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => copyVerse(dailyVerse.verse)}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <Copy size={16} />
                  <span>Copy</span>
                </button>
                <button
                  onClick={() => shareVerse(dailyVerse.verse)}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Share2 size={16} />
                  <span>Share</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Selected Chapter Verses */}
        {selectedChapter && verses.length > 0 && (
          <div className="mt-12">
            <div className="mb-8">
              <h2 className="text-3xl font-bold mb-2 arabic-text">
                {selectedChapter.name_arabic}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                {selectedChapter.name_simple} -{" "}
                {selectedChapter.translated_name?.name}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                {selectedChapter.revelation_place} •{" "}
                {selectedChapter.verses_count} verses
              </p>
            </div>

            <div className="space-y-6">
              {verses.map((verse) => (
                <div key={verse.id} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium text-primary-600">
                      Verse {verse.verse_number}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => copyVerse(verse)}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Copy size={16} />
                      </button>
                      <button
                        onClick={() => shareVerse(verse)}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Share2 size={16} />
                      </button>
                    </div>
                  </div>
                  <div className="arabic-text text-2xl mb-4 leading-relaxed">
                    {verse.text_uthmani}
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    {verse.translations?.[0]?.text}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-8 text-center">
              <button
                onClick={() => setSelectedChapter(null)}
                className="btn-secondary"
              >
                Back to Chapters
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Quran;
