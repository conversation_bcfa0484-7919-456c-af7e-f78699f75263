import { useState, useEffect } from "react";
import { eventsAPI } from "../services/api";
import { useAuth } from "../contexts/AuthContext";
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Plus,
  Filter,
  Search,
  MessageCircle,
  UserCheck,
  UserX,
} from "lucide-react";
import toast from "react-hot-toast";

const Events = () => {
  const { user, isAuthenticated } = useAuth();
  const [events, setEvents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [userLocation, setUserLocation] = useState(null);

  useEffect(() => {
    fetchEvents();
    fetchCategories();
    getUserLocation();
  }, [selectedCategory]);

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => {
          console.error("Geolocation error:", error);
        }
      );
    }
  };

  const fetchEvents = async () => {
    try {
      setIsLoading(true);
      const params = {
        category: selectedCategory,
        upcoming: true,
        ...(userLocation && {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          radius: 50000, // 50km
        }),
      };

      const response = await eventsAPI.getEvents(
        1,
        params.category,
        params.upcoming,
        params.latitude,
        params.longitude,
        params.radius
      );
      setEvents(response.data.data.events || []);
    } catch (error) {
      console.error("Failed to fetch events:", error);
      toast.error("Failed to load events");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await eventsAPI.getEventCategories();
      setCategories(response.data.data.categories || []);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    }
  };

  const handleRSVP = async (eventId, isRegistering = true) => {
    if (!isAuthenticated) {
      toast.error("Please login to RSVP for events");
      return;
    }

    try {
      if (isRegistering) {
        await eventsAPI.registerForEvent(eventId);
        toast.success("Successfully registered for event!");
      } else {
        await eventsAPI.unregisterFromEvent(eventId);
        toast.success("Successfully unregistered from event");
      }

      // Refresh events to update participant count
      fetchEvents();
    } catch (error) {
      const message = error.response?.data?.message || "Failed to update RSVP";
      toast.error(message);
    }
  };

  const openEventDetails = async (eventId) => {
    try {
      const response = await eventsAPI.getEvent(eventId);
      setSelectedEvent(response.data.data.event);
      setShowEventModal(true);
    } catch (error) {
      console.error("Failed to fetch event details:", error);
      toast.error("Failed to load event details");
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCategory = (category) => {
    return category
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const filteredEvents = events.filter(
    (event) =>
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Community Events
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Join Islamic study circles, community gatherings, and spiritual
              events
            </p>
          </div>

          {isAuthenticated && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center space-x-2 mt-4 md:mt-0"
            >
              <Plus size={20} />
              <span>Create Event</span>
            </button>
          )}
        </div>

        {/* Filters */}
        <div className="mb-8 space-y-4 md:space-y-0 md:flex md:items-center md:space-x-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search events..."
              className="input-field pl-10"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-2">
            <Filter size={20} className="text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input-field min-w-48"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {formatCategory(category)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Events Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event) => (
              <div
                key={event._id}
                className="card p-6 hover:shadow-lg transition-shadow"
              >
                {/* Event Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <span className="inline-block px-2 py-1 text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 rounded-full mb-2">
                      {formatCategory(event.category)}
                    </span>
                    <h3 className="text-xl font-semibold mb-2 line-clamp-2">
                      {event.title}
                    </h3>
                  </div>
                </div>

                {/* Event Details */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <Clock size={16} className="mr-2" />
                    <span className="text-sm">
                      {formatDate(event.startDate)}
                    </span>
                  </div>

                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <MapPin size={16} className="mr-2" />
                    <span className="text-sm">
                      {event.location.type === "online"
                        ? "Online Event"
                        : event.location.venue?.name || "Physical Location"}
                    </span>
                  </div>

                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <Users size={16} className="mr-2" />
                    <span className="text-sm">
                      {event.participantCount || 0} registered
                      {event.capacity && ` / ${event.capacity} max`}
                    </span>
                  </div>
                </div>

                {/* Event Description */}
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                  {event.description}
                </p>

                {/* Organizer */}
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm font-medium">
                      {event.organizer?.firstName?.[0]?.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      {event.organizer?.firstName} {event.organizer?.lastName}
                    </p>
                    <p className="text-xs text-gray-500">Organizer</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => openEventDetails(event._id)}
                    className="flex-1 btn-secondary text-sm py-2"
                  >
                    View Details
                  </button>

                  {isAuthenticated && (
                    <button
                      onClick={() => {
                        const isRegistered = event.participants?.some(
                          (p) => p.user === user._id
                        );
                        handleRSVP(event._id, !isRegistered);
                      }}
                      className={`flex-1 text-sm py-2 px-4 rounded-lg font-medium transition-colors ${
                        event.participants?.some((p) => p.user === user._id)
                          ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-300"
                          : "btn-primary"
                      }`}
                    >
                      {event.participants?.some((p) => p.user === user._id) ? (
                        <>
                          <UserX size={16} className="inline mr-1" />
                          Leave
                        </>
                      ) : (
                        <>
                          <UserCheck size={16} className="inline mr-1" />
                          Join
                        </>
                      )}
                    </button>
                  )}
                </div>

                {/* Circle Chat Button */}
                {event.isCircle &&
                  event.participants?.some((p) => p.user === user._id) && (
                    <button
                      onClick={() => openEventDetails(event._id)}
                      className="w-full mt-2 btn-islamic text-sm py-2 flex items-center justify-center space-x-2"
                    >
                      <MessageCircle size={16} />
                      <span>Join Circle Chat</span>
                    </button>
                  )}
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No events found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {searchQuery || selectedCategory
                  ? "Try adjusting your search or filters"
                  : "Be the first to create an event for the community!"}
              </p>
              {isAuthenticated && !searchQuery && !selectedCategory && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="btn-primary mt-4"
                >
                  Create First Event
                </button>
              )}
            </div>
          )}
        </div>

        {/* Create Event Modal */}
        {showCreateModal && (
          <CreateEventModal
            onClose={() => setShowCreateModal(false)}
            onEventCreated={() => {
              setShowCreateModal(false);
              fetchEvents();
            }}
            categories={categories}
          />
        )}

        {/* Event Details Modal */}
        {showEventModal && selectedEvent && (
          <EventDetailsModal
            event={selectedEvent}
            onClose={() => {
              setShowEventModal(false);
              setSelectedEvent(null);
            }}
            onRSVP={handleRSVP}
            user={user}
            isAuthenticated={isAuthenticated}
          />
        )}
      </div>
    </div>
  );
};

// Create Event Modal Component
const CreateEventModal = ({ onClose, onEventCreated, categories }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "",
    startDate: "",
    endDate: "",
    locationType: "physical",
    venue: "",
    capacity: "",
    isCircle: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const eventData = {
        ...formData,
        location: {
          type: formData.locationType,
          venue:
            formData.locationType === "physical"
              ? { name: formData.venue }
              : null,
        },
        capacity: formData.capacity ? parseInt(formData.capacity) : null,
      };

      await eventsAPI.createEvent(eventData);
      toast.success("Event created successfully!");
      onEventCreated();
    } catch (error) {
      const message = error.response?.data?.message || "Failed to create event";
      toast.error(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-6">Create New Event</h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Event Title
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                className="input-field"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                className="input-field"
                rows={4}
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Category
                </label>
                <select
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({ ...formData, category: e.target.value })
                  }
                  className="input-field"
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category
                        .split("_")
                        .map(
                          (word) => word.charAt(0).toUpperCase() + word.slice(1)
                        )
                        .join(" ")}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Capacity (Optional)
                </label>
                <input
                  type="number"
                  value={formData.capacity}
                  onChange={(e) =>
                    setFormData({ ...formData, capacity: e.target.value })
                  }
                  className="input-field"
                  min="1"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Start Date & Time
                </label>
                <input
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) =>
                    setFormData({ ...formData, startDate: e.target.value })
                  }
                  className="input-field"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  End Date & Time
                </label>
                <input
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) =>
                    setFormData({ ...formData, endDate: e.target.value })
                  }
                  className="input-field"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Location Type
              </label>
              <select
                value={formData.locationType}
                onChange={(e) =>
                  setFormData({ ...formData, locationType: e.target.value })
                }
                className="input-field"
              >
                <option value="physical">Physical Location</option>
                <option value="online">Online Event</option>
                <option value="hybrid">Hybrid (Physical + Online)</option>
              </select>
            </div>

            {formData.locationType !== "online" && (
              <div>
                <label className="block text-sm font-medium mb-2">Venue</label>
                <input
                  type="text"
                  value={formData.venue}
                  onChange={(e) =>
                    setFormData({ ...formData, venue: e.target.value })
                  }
                  className="input-field"
                  placeholder="Enter venue name or address"
                  required
                />
              </div>
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isCircle"
                checked={formData.isCircle}
                onChange={(e) =>
                  setFormData({ ...formData, isCircle: e.target.checked })
                }
                className="mr-2"
              />
              <label htmlFor="isCircle" className="text-sm">
                Make this an ongoing study circle with chat
              </label>
            </div>

            <div className="flex space-x-4 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 btn-primary"
              >
                {isSubmitting ? "Creating..." : "Create Event"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Event Details Modal Component (simplified for now)
const EventDetailsModal = ({
  event,
  onClose,
  onRSVP,
  user,
  isAuthenticated,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <h2 className="text-3xl font-bold">{event.title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>

          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              {event.description}
            </p>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Event Details</h4>
                <div className="space-y-2 text-sm">
                  <p>
                    <strong>Category:</strong> {event.category}
                  </p>
                  <p>
                    <strong>Start:</strong>{" "}
                    {new Date(event.startDate).toLocaleString()}
                  </p>
                  <p>
                    <strong>End:</strong>{" "}
                    {new Date(event.endDate).toLocaleString()}
                  </p>
                  <p>
                    <strong>Location:</strong> {event.location.type}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">
                  Participants ({event.participantCount || 0})
                </h4>
                <div className="space-y-1">
                  {event.participants?.slice(0, 5).map((participant, index) => (
                    <p key={index} className="text-sm">
                      {participant.user?.firstName} {participant.user?.lastName}
                    </p>
                  ))}
                  {event.participants?.length > 5 && (
                    <p className="text-sm text-gray-500">
                      +{event.participants.length - 5} more
                    </p>
                  )}
                </div>
              </div>
            </div>

            {isAuthenticated && (
              <div className="pt-4 border-t">
                <button
                  onClick={() => {
                    const isRegistered = event.participants?.some(
                      (p) => p.user === user._id
                    );
                    onRSVP(event._id, !isRegistered);
                    onClose();
                  }}
                  className={`btn-primary ${
                    event.participants?.some((p) => p.user === user._id)
                      ? "bg-red-600 hover:bg-red-700"
                      : ""
                  }`}
                >
                  {event.participants?.some((p) => p.user === user._id)
                    ? "Leave Event"
                    : "Join Event"}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Events;
