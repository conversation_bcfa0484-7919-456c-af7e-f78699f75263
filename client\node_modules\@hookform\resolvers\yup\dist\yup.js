var e=require("@hookform/resolvers"),t=require("react-hook-form");exports.yupResolver=function(r,o,n){return void 0===n&&(n={}),function(s,a,i){try{return Promise.resolve(function(t,u){try{var c=(null!=o&&o.context&&"development"===process.env.NODE_ENV&&console.warn("You should not used the yup options context. Please, use the 'useForm' context object instead"),Promise.resolve(r["sync"===n.mode?"validateSync":"validate"](s,Object.assign({abortEarly:!1},o,{context:a}))).then(function(t){return i.shouldUseNativeValidation&&e.validateFieldsNatively({},i),{values:n.raw?Object.assign({},s):t,errors:{}}}))}catch(e){return u(e)}return c&&c.then?c.then(void 0,u):c}(0,function(r){if(!r.inner)throw r;return{values:{},errors:e.toNestErrors((o=r,n=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,(o.inner||[]).reduce(function(e,r){if(e[r.path]||(e[r.path]={message:r.message,type:r.type}),n){var o=e[r.path].types,s=o&&o[r.type];e[r.path]=t.appendErrors(r.path,n,e,r.type,s?[].concat(s,r.message):r.message)}return e},{})),i)};var o,n}))}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=yup.js.map
