{"mappings": ";;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;;;;AAqB3G,SAAS,0CAA6D,KAAyB;IACpG,IAAI,cACF,UAAU,EACV,SAAS,WAAW,EACpB,QAAQ,UAAU,iBAClB,aAAa,EACd,GAAG;IAEJ,MAAM,SAAuC,CAAA,GAAA,kBAAU,EAAE,CAAC;QACxD,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC,IAAI,YACF,WAAW;YAGb,IAAI,eACF,cAAc;YAGhB,OAAO;QACT;IACF,GAAG;QAAC;QAAY;KAAc;IAG9B,MAAM,mBAAmB,CAAA,GAAA,yCAAoB,EAAU;IAEvD,MAAM,UAAyC,CAAA,GAAA,kBAAU,EAAE,CAAC;QAC1D,kGAAkG;QAClG,oDAAoD;QAEpD,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,gBAAgB,CAAA,GAAA,uBAAe,EAAE,iBAAiB,CAAA,GAAA,uBAAe;QACvF,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,kBAAkB,CAAA,GAAA,qBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,aACF,YAAY;YAGd,IAAI,eACF,cAAc;YAGhB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAe;QAAa;KAAiB;IAEjD,OAAO;QACL,YAAY;YACV,SAAS,AAAC,CAAC,cAAe,CAAA,eAAe,iBAAiB,UAAS,IAAM,UAAU;YACnF,QAAQ,AAAC,CAAC,cAAe,CAAA,cAAc,aAAY,IAAM,SAAS;QACpE;IACF;AACF", "sources": ["packages/@react-aria/interactions/src/useFocus.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useFocus.module.js.map"}