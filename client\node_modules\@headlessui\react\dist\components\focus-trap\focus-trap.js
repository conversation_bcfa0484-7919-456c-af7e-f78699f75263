"use client";import F,{useRef as M}from"react";import{useDisposables as W}from'../../hooks/use-disposables.js';import{useEvent as O}from'../../hooks/use-event.js';import{useEventListener as K}from'../../hooks/use-event-listener.js';import{useIsMounted as P}from'../../hooks/use-is-mounted.js';import{useIsTopLayer as C}from'../../hooks/use-is-top-layer.js';import{useOnUnmount as q}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as J}from'../../hooks/use-owner.js';import{useServerHandoffComplete as X}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as z}from'../../hooks/use-sync-refs.js';import{Direction as y,useTabDirection as Q}from'../../hooks/use-tab-direction.js';import{useWatch as R}from'../../hooks/use-watch.js';import{Hidden as _,HiddenFeatures as S}from'../../internal/hidden.js';import{history as H}from'../../utils/active-element-history.js';import*as T from'../../utils/dom.js';import{Focus as i,FocusResult as h,focusElement as p,focusIn as d}from'../../utils/focus-management.js';import{match as j}from'../../utils/match.js';import{microTask as U}from'../../utils/micro-task.js';import{forwardRefWithAs as Y,useRender as Z}from'../../utils/render.js';function x(s){if(!s)return new Set;if(typeof s=="function")return new Set(s());let e=new Set;for(let t of s.current)T.isElement(t.current)&&e.add(t.current);return e}let $="div";var G=(n=>(n[n.None=0]="None",n[n.InitialFocus=1]="InitialFocus",n[n.TabLock=2]="TabLock",n[n.FocusLock=4]="FocusLock",n[n.RestoreFocus=8]="RestoreFocus",n[n.AutoFocus=16]="AutoFocus",n))(G||{});function D(s,e){let t=M(null),r=z(t,e),{initialFocus:o,initialFocusFallback:a,containers:n,features:u=15,...f}=s;X()||(u=0);let l=J(t);te(u,{ownerDocument:l});let m=re(u,{ownerDocument:l,container:t,initialFocus:o,initialFocusFallback:a});ne(u,{ownerDocument:l,container:t,containers:n,previousActiveElement:m});let g=Q(),v=O(c=>{if(!T.isHTMLElement(t.current))return;let E=t.current;(V=>V())(()=>{j(g.current,{[y.Forwards]:()=>{d(E,i.First,{skipElements:[c.relatedTarget,a]})},[y.Backwards]:()=>{d(E,i.Last,{skipElements:[c.relatedTarget,a]})}})})}),A=C(!!(u&2),"focus-trap#tab-lock"),N=W(),b=M(!1),k={ref:r,onKeyDown(c){c.key=="Tab"&&(b.current=!0,N.requestAnimationFrame(()=>{b.current=!1}))},onBlur(c){if(!(u&4))return;let E=x(n);T.isHTMLElement(t.current)&&E.add(t.current);let L=c.relatedTarget;T.isHTMLorSVGElement(L)&&L.dataset.headlessuiFocusGuard!=="true"&&(I(E,L)||(b.current?d(t.current,j(g.current,{[y.Forwards]:()=>i.Next,[y.Backwards]:()=>i.Previous})|i.WrapAround,{relativeTo:c.target}):T.isHTMLorSVGElement(c.target)&&p(c.target)))}},B=Z();return F.createElement(F.Fragment,null,A&&F.createElement(_,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:S.Focusable}),B({ourProps:k,theirProps:f,defaultTag:$,name:"FocusTrap"}),A&&F.createElement(_,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:S.Focusable}))}let w=Y(D),Re=Object.assign(w,{features:G});function ee(s=!0){let e=M(H.slice());return R(([t],[r])=>{r===!0&&t===!1&&U(()=>{e.current.splice(0)}),r===!1&&t===!0&&(e.current=H.slice())},[s,H,e]),O(()=>{var t;return(t=e.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function te(s,{ownerDocument:e}){let t=!!(s&8),r=ee(t);R(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&p(r())},[t]),q(()=>{t&&p(r())})}function re(s,{ownerDocument:e,container:t,initialFocus:r,initialFocusFallback:o}){let a=M(null),n=C(!!(s&1),"focus-trap#initial-focus"),u=P();return R(()=>{if(s===0)return;if(!n){o!=null&&o.current&&p(o.current);return}let f=t.current;f&&U(()=>{if(!u.current)return;let l=e==null?void 0:e.activeElement;if(r!=null&&r.current){if((r==null?void 0:r.current)===l){a.current=l;return}}else if(f.contains(l)){a.current=l;return}if(r!=null&&r.current)p(r.current);else{if(s&16){if(d(f,i.First|i.AutoFocus)!==h.Error)return}else if(d(f,i.First)!==h.Error)return;if(o!=null&&o.current&&(p(o.current),(e==null?void 0:e.activeElement)===o.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=e==null?void 0:e.activeElement})},[o,n,s]),a}function ne(s,{ownerDocument:e,container:t,containers:r,previousActiveElement:o}){let a=P(),n=!!(s&4);K(e==null?void 0:e.defaultView,"focus",u=>{if(!n||!a.current)return;let f=x(r);T.isHTMLElement(t.current)&&f.add(t.current);let l=o.current;if(!l)return;let m=u.target;T.isHTMLElement(m)?I(f,m)?(o.current=m,p(m)):(u.preventDefault(),u.stopPropagation(),p(l)):p(o.current)},!0)}function I(s,e){for(let t of s)if(t.contains(e))return!0;return!1}export{Re as FocusTrap,G as FocusTrapFeatures};
