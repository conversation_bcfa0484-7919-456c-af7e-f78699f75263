{"version": 3, "file": "arktype.modern.mjs", "sources": ["../../node_modules/@standard-schema/utils/dist/index.js", "../src/arktype.ts"], "sourcesContent": ["// src/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path?.length) {\n    let dotPath = \"\";\n    for (const item of issue.path) {\n      const key = typeof item === \"object\" ? item.key : item;\n      if (typeof key === \"string\" || typeof key === \"number\") {\n        if (dotPath) {\n          dotPath += `.${key}`;\n        } else {\n          dotPath += key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return dotPath;\n  }\n  return null;\n}\n\n// src/SchemaError/SchemaError.ts\nvar SchemaError = class extends Error {\n  /**\n   * The schema issues.\n   */\n  issues;\n  /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"SchemaError\";\n    this.issues = issues;\n  }\n};\nexport {\n  SchemaError,\n  getDotPath\n};\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport { getDotPath } from '@standard-schema/utils';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  issues: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n\n  for (let i = 0; i < issues.length; i++) {\n    const error = issues[i];\n    const path = getDotPath(error);\n\n    if (path) {\n      if (!errors[path]) {\n        errors[path] = { message: error.message, type: '' };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = errors[path].types || {};\n\n        errors[path].types = {\n          ...types,\n          [Object.keys(types).length]: error.message,\n        };\n      }\n    }\n  }\n\n  return errors;\n}\n\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Arktype schema validation\n * @param {Schema} schema - The Arktype schema to validate against\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='raw'] - Return the raw input values rather than the parsed values\n * @returns {Resolver<Schema['inferOut']>} A resolver function compatible with react-hook-form\n * @example\n * const schema = type({\n *   username: 'string>2'\n * });\n *\n * useForm({\n *   resolver: arktypeResolver(schema)\n * });\n */\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Input | Output> {\n  return async (values: Input, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["getDotPath", "issue", "path", "length", "dotPath", "item", "key", "arktypeResolver", "schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "result", "validate", "Promise", "issues", "errors", "validateAllFieldCriteria", "i", "error", "message", "type", "types", "_extends", "Object", "keys", "parseErrorSchema", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "assign", "value"], "mappings": "uSACA,SAASA,EAAWC,GAClB,GAAIA,EAAMC,MAAMC,OAAQ,CACtB,IAAIC,EAAU,GACd,IAAK,MAAMC,KAAQJ,EAAMC,KAAM,CAC7B,MAAMI,EAAsB,iBAATD,EAAoBA,EAAKC,IAAMD,EAClD,GAAmB,iBAARC,GAAmC,iBAARA,EAOpC,OAAO,KALLF,GADEA,EACS,IAAIE,IAEJA,CAKhB,CACD,OAAOF,CACR,CACD,OAAO,IACT,CC8CgB,SAAAG,EACdC,EACAC,EACAC,EAEI,CAAE,GAEN,OAAcC,MAAAA,EAAeC,EAAGC,KAC9B,IAAIC,EAASN,EAAO,aAAaO,SAASJ,GAK1C,GAJIG,aAAkBE,UACpBF,QAAeA,GAGbA,EAAOG,OAAQ,CACjB,MAAMC,EA1EZ,SACED,EACAE,GAEA,MAAMD,EAAqC,CAAA,EAE3C,IAAK,IAAIE,EAAI,EAAGA,EAAIH,EAAOd,OAAQiB,IAAK,CACtC,MAAMC,EAAQJ,EAAOG,GACflB,EAAOF,EAAWqB,GAExB,GAAInB,IACGgB,EAAOhB,KACVgB,EAAOhB,GAAQ,CAAEoB,QAASD,EAAMC,QAASC,KAAM,KAG7CJ,GAA0B,CAC5B,MAAMK,EAAQN,EAAOhB,GAAMsB,OAAS,CAAA,EAEpCN,EAAOhB,GAAMsB,MAAKC,KACbD,EAAK,CACR,CAACE,OAAOC,KAAKH,GAAOrB,QAASkB,EAAMC,SAEvC,CAEJ,CAEA,OAAOJ,CACT,CA+CqBU,CACbd,EAAOG,QACNJ,EAAQgB,2BAAsD,QAAzBhB,EAAQiB,cAGhD,MAAO,CACLnB,OAAQ,CAAE,EACVO,OAAQa,EAAab,EAAQL,GAEjC,CAIA,OAFAA,EAAQgB,2BAA6BG,EAAuB,CAAE,EAAEnB,GAEzD,CACLF,OAAQD,EAAgBuB,IAAMP,OAAOQ,OAAO,CAAA,EAAIvB,GAAUG,EAAOqB,MACjEjB,OAAQ,IAGd"}